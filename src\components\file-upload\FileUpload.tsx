import { useState, useCallback, useRef, forwardRef, useImperativeHandle } from 'react'
import { message } from 'antd'
import { useSimpleTranslation } from '../../i18n/simple-hooks'
import './FileUpload.css'

interface FileUploadProps {
  onFilesUploaded: (files: File[]) => void | Promise<void>
  children: React.ReactNode
  allowedFileTypes?: string[]
  disabled?: boolean
  isLoading?: boolean
  maxFiles?: number
  currentFileCount?: number // 当前已上传文件数量
}

// 导出一个触发文件选择的函数类型
export interface FileUploadRef {
  triggerFileSelect: () => void
}

const FileUpload = forwardRef<FileUploadRef, FileUploadProps>(({ onFilesUploaded, children, allowedFileTypes = [], disabled = false, isLoading = false, maxFiles = 2, currentFileCount = 0 }, ref) => {
  const { t } = useSimpleTranslation()
  const [isDragOver, setIsDragOver] = useState(false)
  const [dragCounter, setDragCounter] = useState(0)
  const dragRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    triggerFileSelect: () => {
      if (!disabled && fileInputRef.current) {
        fileInputRef.current.click()
      }
    }
  }), [disabled])

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    console.log('=====', '用户进入了拖拽区域')
    e.preventDefault()
    e.stopPropagation()

    if (disabled) return

    setDragCounter(prev => prev + 1)

    // 检查是否包含文件
    if (e.dataTransfer.types && e.dataTransfer.types.includes('Files')) {
      setIsDragOver(true)
    }
  }, [disabled])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    console.log('=====', '用户在拖拽区域')
    e.preventDefault()
    e.stopPropagation()

    if (disabled) return

    // 设置拖拽效果
    e.dataTransfer.dropEffect = 'copy'
  }, [disabled])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    console.log('=====', '用户离开了拖拽区域')
    e.preventDefault()
    e.stopPropagation()

    if (disabled) return

    setDragCounter(prev => {
      const newCounter = prev - 1
      if (newCounter === 0) {
        setIsDragOver(false)
      }
      return newCounter
    })
  }, [disabled])

  const validateFiles = useCallback((files: File[]) => {
    // 检查文件数量限制
    const availableSlots = maxFiles - currentFileCount
    if (availableSlots <= 0) {
      message.error(t('chat.maxFilesReached').replace('{count}', maxFiles.toString()))
      return []
    }

    if (files.length > availableSlots) {
      message.error(t('chat.maxFilesExceeded')
        .replace('{available}', availableSlots.toString())
        .replace('{max}', maxFiles.toString()))
      return []
    }

    // 检查文件类型
    if (allowedFileTypes.length === 0) return files

    const validFiles = files.filter(file => {
      const fileExtension = file.name.split('.').pop()?.toLowerCase()
      return fileExtension && allowedFileTypes.includes(fileExtension)
    })

    if (validFiles.length < files.length) {
      message.error(t('chat.unsupportedFileType')
        .replace('{fileName}', '部分文件')
        .replace('{formats}', allowedFileTypes.join(', ')))
    }

    return validFiles
  }, [allowedFileTypes, maxFiles, currentFileCount, t])

  const handleDrop = useCallback((e: React.DragEvent) => {
    console.log('=====', '用户松开了鼠标', disabled)
    e.preventDefault()
    e.stopPropagation()

    if (disabled) return

    setIsDragOver(false)
    setDragCounter(0)

    const files = Array.from(e.dataTransfer.files)
    console.log('files', files)
    if (files.length > 0) {
      const validFiles = validateFiles(files)
      if (validFiles.length > 0) {
        onFilesUploaded(validFiles)
      }
    }
  }, [onFilesUploaded, validateFiles, disabled])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('=====', '用户选择了文件')
    if (disabled) return

    const files = Array.from(e.target.files || [])
    if (files.length > 0) {
      const validFiles = validateFiles(files)
      if (validFiles.length > 0) {
        onFilesUploaded(validFiles)
      }
    }
    // 清空input值，允许重复选择同一文件
    e.target.value = ''
  }, [onFilesUploaded, validateFiles, disabled])

  const acceptString = allowedFileTypes.length > 0
    ? allowedFileTypes.map(type => `.${type}`).join(',')
    : '.pdf,.doc,.docx,.txt,.md,.xlsx,.xls,.pptx,.ppt,.csv'

  return (
    <div
      ref={dragRef}
      className={`relative transition-all duration-300 ease-in-out ${
        isDragOver
          ? 'ring-2 ring-blue-500 ring-opacity-50 bg-blue-50 bg-opacity-20'
          : ''
      } ${disabled ? 'pointer-events-none opacity-50' : ''}`}
      onDragEnter={handleDragEnter}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {children}

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple={maxFiles - currentFileCount > 1}
        accept={acceptString}
        onChange={handleFileSelect}
        style={{ 
          display: 'none', 
          position: 'absolute', 
          left: '-9999px', 
          top: '-9999px',
          opacity: 0, 
          visibility: 'hidden',
          width: 0,
          height: 0
        }}
        className="sr-only"
        tabIndex={-1}
        disabled={disabled || currentFileCount >= maxFiles}
      />

      {/* 拖拽覆盖层 */}
      {isDragOver && !disabled && (
        <div className="absolute inset-0 drag-overlay border-2 border-dashed border-blue-400 rounded-2xl flex items-center justify-center z-50 fade-in">
          <div className="text-center p-6">
            <div className="text-4xl mb-3 file-icon-bounce">📎</div>
            <div className="text-blue-600 font-semibold text-lg mb-2">{t('fileUpload.dropToUpload')}</div>
            <div className="text-sm text-blue-500 space-y-1">
              {maxFiles && (
                <div className="font-medium">
                  {t('fileUpload.maxFiles').replace('{count}', maxFiles.toString())}
                </div>
              )}
              <div>
                {allowedFileTypes.length > 0 ? (() => {
                  // 首页格式筛选：仅显示文档类格式
                  const documentFormats = ['pdf', 'doc', 'docx', 'txt', 'md', 'xlsx', 'xls', 'pptx', 'ppt', 'csv', 'html', 'xml', 'epub', 'json'];
                  const filteredFormats = allowedFileTypes.filter(type =>
                    documentFormats.includes(type.toLowerCase())
                  );
                  return filteredFormats.length > 0
                    ? `${t('fileUpload.supportedFormats')}: ${filteredFormats.join(', ')}`
                    : t('fileUpload.multiFileSupport');
                })() : t('fileUpload.multiFileSupport')}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 上传加载状态覆盖层 */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-90 rounded-2xl flex items-center justify-center z-40 fade-in">
          <div className="text-center p-6">
            <div className="text-3xl mb-3 animate-spin">⏳</div>
            <div className="text-gray-600 font-medium text-lg mb-2">{t('fileUpload.uploading')}</div>
            <div className="text-sm text-gray-500">{t('fileUpload.pleaseWait')}</div>
          </div>
        </div>
      )}
    </div>
  )
})

FileUpload.displayName = 'FileUpload'

export default FileUpload
